import React, { useState, useRef, memo, useCallback, useMemo, useEffect } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, TextInput, Animated, Alert, ImageBackground, Platform, KeyboardAvoidingView } from "react-native"
import { useTheme } from "../components/ThemeContext"
import { useOptimizedAnimation, usePerformanceMonitor } from "../utils/performance"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../types/navigation"
import ApiService from "../services/apiService"
import ArrowRightIcon from "../components/icons/ArrowRightIcon"
import { logger } from '../services/productionLogger';
import ReactNativeHapticFeedback from "react-native-haptic-feedback"
import Svg, { Path } from "react-native-svg"
import { navigationHandler } from '../handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "EmailInput">

const EmailInputScreen = memo<Props>(({ }) => {
  const { theme, isDark } = useTheme()
  const [email, setEmail] = useState("")
  const [isEmailFocused, setIsEmailFocused] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false)
  const [isContinuePressed, setIsContinuePressed] = useState(false)
  const [isEmailValid, setIsEmailValid] = useState(false)
  const [showError, setShowError] = useState(false)
  const [showToast, setShowToast] = useState(false)
  const toastAnim = useRef(new Animated.Value(300)).current
  const emailInputRef = useRef<TextInput>(null)

  // Performance monitoring
  usePerformanceMonitor('EmailInputScreen')

  // Optimized animations
  const { animatedValue: fadeAnim, animate: animateFade } = useOptimizedAnimation(0)
  const { animatedValue: slideUpAnim, animate: animateSlideUp } = useOptimizedAnimation(30)
  const labelAnim = useRef(new Animated.Value(0)).current
  const iconAnim = useRef(new Animated.Value(0)).current
  const continueButtonScale = useRef(new Animated.Value(1)).current
  const shakeAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()
  }, [fadeAnim, slideUpAnim])

  // Improved haptic feedback utility
  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' | 'success' | 'error') => {
    const hapticOptions = {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: true, // Force vibration even if system settings are off
    }

    try {
      switch (type) {
        case 'light':
          ReactNativeHapticFeedback.trigger("impactLight", hapticOptions)
          break
        case 'medium':
          ReactNativeHapticFeedback.trigger("impactMedium", hapticOptions)
          break
        case 'heavy':
          ReactNativeHapticFeedback.trigger("impactHeavy", hapticOptions)
          break
        case 'success':
          ReactNativeHapticFeedback.trigger("notificationSuccess", hapticOptions)
          break
        case 'error':
          ReactNativeHapticFeedback.trigger("notificationError", hapticOptions)
          break
      }
    } catch (error) {
      logger.warn('Haptic feedback failed', error, 'email_input');
    }
  }, [])

  // Enhanced email sanitization and validation
  const sanitizeEmail = useCallback((emailInput: string): string => {
    return emailInput.trim().toLowerCase().replace(/[<>]/g, '');
  }, []);

  const validateEmailSecurity = useCallback((emailInput: string): { isValid: boolean; error?: string } => {
    const sanitized = sanitizeEmail(emailInput);
    
    // Basic format check
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(sanitized)) {
      return { isValid: false, error: 'Invalid email format' };
    }
    
    // Check for suspicious patterns
    if (sanitized.includes('script') || sanitized.includes('javascript:')) {
      return { isValid: false, error: 'Invalid email content' };
    }
    
    return { isValid: true };
  }, [sanitizeEmail]);

  // Check email validity
  useEffect(() => {
    const validation = validateEmailSecurity(email);
    const valid = validation.isValid
    setIsEmailValid(valid)
    if (!valid && showError) {
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start()
      // Trigger error haptic
      triggerHaptic('error')
      // Show toast notification
      setShowToast(true)
      Animated.spring(toastAnim, {
        toValue: 0,
        friction: 5,
        tension: 40,
        useNativeDriver: true,
      }).start()
      // Hide toast and error after 3 seconds
      const timer = setTimeout(() => {
        Animated.timing(toastAnim, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          setShowToast(false)
          setShowError(false)
        })
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [email, showError, triggerHaptic, toastAnim])

  // Memoized handlers for performance
  const handleContinue = useCallback(async () => {
    if (email.trim() && !isLoading) {
      // Enhanced email validation with sanitization
      const sanitizedEmail = sanitizeEmail(email);
      const validation = validateEmailSecurity(sanitizedEmail);
      
      if (!validation.isValid) {
        logger.security('INVALID_EMAIL_INPUT_ATTEMPT', {
          emailDomain: sanitizedEmail.split('@')[1] || 'unknown',
          error: validation.error
        });
        setShowError(true)
        triggerHaptic('error')
        Animated.sequence([
          Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
          Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
        ]).start()
        Alert.alert('Invalid Email', validation.error || 'Please enter a valid email address');
        return
      }

      setIsLoading(true)

      try {
        logger.userAction('email_otp_requested', {
          emailDomain: sanitizedEmail.split('@')[1] || 'unknown',
          hasEmail: !!sanitizedEmail
        });
        
        const response = await ApiService.sendEmailOTP(sanitizedEmail, 'verification')

        if (response.status === 'success' || response.data?.status === 'success') {
          logger.info('Email OTP sent successfully', {
            emailDomain: sanitizedEmail.split('@')[1] || 'unknown'
          }, 'email_input');
          triggerHaptic('success')
          navigationHandler.navigateToEmailVerification(sanitizedEmail)
        } else {
          const errorMsg = response.message || response.data?.message || 'Failed to send verification code';
          logger.warn('Failed to send email OTP', {
            error: errorMsg,
            emailDomain: sanitizedEmail.split('@')[1] || 'unknown'
          }, 'email_input');
          Alert.alert('Error', errorMsg)
        }
      } catch (error) {
        logger.error('Error sending email OTP', error, 'email_input');
        const errorMessage = (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as { message?: string }).message
          : undefined;
        Alert.alert('Error', errorMessage || 'Failed to send verification code. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }
  }, [email, isLoading, triggerHaptic])

  const handleBack = useCallback(() => {
    triggerHaptic('light')
    navigationHandler.goBack()
  }, [triggerHaptic])

  // Memoized focus handlers
  const handleFocus = useCallback(() => {
    setIsEmailFocused(true)
    Animated.parallel([
      Animated.timing(labelAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(iconAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      })
    ]).start()
    if (emailInputRef.current) {
      emailInputRef.current.focus();
    }
    triggerHaptic('light')
  }, [labelAnim, iconAnim, triggerHaptic])

  const handleBlur = useCallback(() => {
    setIsEmailFocused(false)
    if (!email) {
      Animated.parallel([
        Animated.timing(labelAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
        Animated.timing(iconAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        })
      ]).start()
    }
    if (email && !isEmailValid) {
      setShowError(true)
    } else {
      setShowError(false)
    }
  }, [email, labelAnim, iconAnim, isEmailValid])

  // Memoized styles for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 44,
      height: 44,
      justifyContent: 'center',
      alignItems: 'center',
    },
    backButtonPressed: {
      transform: [{ scale: 0.95 }],
    },
    content: {
      flex: 1,
      paddingHorizontal: 30,
      paddingTop: 40,
    },
    inputContainer: {
      marginBottom: 80,
      position: 'relative',
      paddingTop: 25, // Space for floating label
      paddingBottom: 10,
      paddingHorizontal: 10,
    },
    iconContainer: {
      position: 'absolute',
      left: 8,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 2,
    },
    emailIcon: {
      width: 20,
      height: 20,
      backgroundColor: 'rgba(128, 128, 128, 0.6)',
      borderRadius: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    floatingLabel: {
      position: 'absolute',
      left: 35, // Back to original position for placeholder
      color: theme.colors.text,
      opacity: 0.7,
      zIndex: 1,
    },
    input: {
      borderWidth: 0,
      borderBottomWidth: 3,
      borderBottomColor: showError && !isEmailValid 
        ? '#FF0000' 
        : isEmailFocused 
          ? '#8A2BE2' // Neon purple when focused
          : (isDark ? 'rgba(128, 128, 128, 0.3)' : 'rgba(128, 128, 128, 0.5)'),
      paddingTop: 8,
      paddingBottom: 8,
      paddingLeft: 5, // Very close to the beginning of the line
      paddingRight: 10,
      fontSize: 18,
      color: theme.colors.text,
      backgroundColor: 'transparent',
      borderRadius: 0,
      textAlign: 'left',
      width: '100%',
      minHeight: 40,
    },
    continueButton: {
      backgroundColor: isDark ? '#FFFFFF' : '#000000',
      borderRadius: 25,
      paddingVertical: 18,
      paddingHorizontal: 60,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 30,
      opacity: (email.trim() && email.includes('@') && email.includes('.') && !isLoading) ? 1 : 0.5,
      transform: [{ scale: continueButtonScale }],
    },
    continueButtonText: {
      color: isDark ? '#000000' : '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
      letterSpacing: 0.5,
    },
    footer: {
      paddingHorizontal: 30,
      paddingBottom: 40,
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
  }), [theme, isDark, isEmailFocused, email, showError, isEmailValid])

  return (
  <View style={styles.container}>
  <StatusBar
  barStyle={isDark ? "light-content" : "dark-content"}
  backgroundColor="transparent"
  translucent={true}
  />
  <ImageBackground
  source={isDark ? require("../../assets/images/bg.jpeg") : require("../../assets/images/bg-white.jpeg")}
  style={StyleSheet.absoluteFillObject}
  resizeMode="cover"
  />
  
  {/* Toast Notification */}
  {showToast && (
    <Animated.View
      style={{
        position: 'absolute',
        top: 40,
        right: 0,
        width: 'auto',
        backgroundColor: '#FF3B30',
        paddingVertical: 12,
        paddingHorizontal: 16,
        zIndex: 1000,
        borderTopLeftRadius: 12,
        borderBottomLeftRadius: 12,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5,
        flexDirection: 'row',
        alignItems: 'center',
        transform: [{ translateX: toastAnim }],
      }}
    >
      <Svg width="20" height="20" viewBox="0 0 24 24" fill="none" style={{ marginRight: 10 }}>
        <Path
          d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z"
          fill="#FFFFFF"
        />
        <Path
          d="M16.29 15.58C16.62 15.9 17.2 15.89 17.52 15.57C17.84 15.24 17.83 14.66 17.5 14.34L14.36 11.2C14.03 10.87 13.45 10.88 13.13 11.21C12.8 11.53 12.81 12.11 13.14 12.44L16.29 15.58Z"
          fill="#FFFFFF"
        />
      </Svg>
      <Text style={{ color: '#FFFFFF', fontSize: 14, fontWeight: '600', maxWidth: 280, fontFamily: 'InterVariable' }}>
        Please enter a valid email address
      </Text>
    </Animated.View>
  )}
  
  {/* Header */}
  <View style={styles.header}>
    <TouchableOpacity 
      style={[
        styles.backButton,
        isBackButtonPressed && styles.backButtonPressed
      ]}
      onPress={handleBack}
      onPressIn={() => setIsBackButtonPressed(true)}
      onPressOut={() => setIsBackButtonPressed(false)}
      activeOpacity={0.8}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <Animated.View 
        style={{ 
          transform: [
            { translateX: isBackButtonPressed ? -1 : 0 }
          ]
        }}
      >
        <BackArrowIcon 
          size={18} 
          color={theme.colors.text} 
        />
      </Animated.View>
    </TouchableOpacity>
  </View>
  
  <Text style={{ fontSize: 20, fontWeight: '600', color: theme.colors.text, marginBottom: 20, textAlign: 'center', fontFamily: 'InterVariable', paddingHorizontal: 30 }}>
    Let's get started – enter your email.
  </Text>
  
  {/* Content */}
  <Animated.View
  style={[
  styles.content,
  {
  opacity: fadeAnim,
  transform: [{ translateY: slideUpAnim }],
  }
  ]}
  >
  <TouchableOpacity 
  style={styles.inputContainer} 
  onPress={() => {
  if (!isEmailFocused) {
  handleFocus();
  }
  }}
  activeOpacity={1}
  >
  <Animated.Text
  style={[
  styles.floatingLabel,
  {
  fontSize: labelAnim.interpolate({
  inputRange: [0, 1],
  outputRange: [18, 14],
  }),
  top: labelAnim.interpolate({
  inputRange: [0, 1],
  outputRange: [30, 0],
  }),
  }
  ]}
  >
  Enter your email
  </Animated.Text>
  <Animated.View 
  style={[
  styles.iconContainer,
  {
  top: iconAnim.interpolate({
  inputRange: [0, 1],
  outputRange: [30, 0],
  }),
  }
  ]}
  >
  <EmailIcon color={isDark ? '#FFFFFF' : '#000000'} />
  </Animated.View>
  <Animated.View style={{ transform: [{ translateX: shakeAnim }] }}>
  <TextInput
  ref={emailInputRef}
  style={styles.input}
  value={email}
  onChangeText={(text) => {
  setEmail(text);
  triggerHaptic('medium');
  }}
  onFocus={handleFocus}
  onBlur={handleBlur}
  placeholder=""
  keyboardType="email-address"
  autoCapitalize="none"
  autoCorrect={false}
  autoComplete="email"
  textContentType="emailAddress"
  cursorColor={isDark ? '#FFFFFF' : '#000000'}
  selectionColor={isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'}
  />
  </Animated.View>
  </TouchableOpacity>
  {/* Error message is now handled by toast notification at the top */}
  </Animated.View>
  
  {/* Footer - Fixed at Bottom */}
  <View style={styles.footer}>
  <TouchableOpacity
  style={styles.continueButton}
  onPress={handleContinue}
  disabled={!email.trim() || isLoading}
  activeOpacity={0.8}
  onPressIn={() => {
  setIsContinuePressed(true);
  triggerHaptic('medium');
  Animated.spring(continueButtonScale, {
  toValue: 0.95,
  useNativeDriver: true,
  friction: 4,
  }).start();
  }}
  onPressOut={() => {
  setIsContinuePressed(false);
  Animated.spring(continueButtonScale, {
  toValue: 1,
  useNativeDriver: true,
  friction: 4,
  }).start();
  }}
  >
  <Text style={styles.continueButtonText}>
  {isLoading ? 'Sending...' : 'Continue'}
  </Text>
  </TouchableOpacity>
  </View>
  </View>
  )
})

const EmailIcon = memo(({ color }: { color: string }) => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
      fill={color}
    />
  </Svg>
))

const BackArrowIcon = memo(({ color, size }: { color: string, size: number }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 11.5H9.41L14.46 6.46C14.85 6.07 14.85 5.44 14.46 5.05C14.27 4.86 14.02 4.76 13.76 4.76C13.5 4.76 13.25 4.86 13.05 5.05L6.46 11.64C6.07 12.03 6.07 12.66 6.46 13.05L13.05 19.64C13.44 20.03 14.07 20.03 14.46 19.64C14.85 19.25 14.85 18.62 14.46 18.23L9.41 13.18H20C20.55 13.18 21 12.73 21 12.18C21 11.63 20.55 11.18 20 11.18V11.5Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
))

EmailInputScreen.displayName = 'EmailInputScreen'

export default EmailInputScreen
