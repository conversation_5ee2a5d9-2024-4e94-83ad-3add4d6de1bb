import { Platform } from 'react-native';
import CryptoJS from 'crypto-js';
import secureStorage from './secureStorageService';
import crashReporting from './crashReportingService';
import performanceService from './performanceService';
import sslPinning from './sslPinningService';
import { logger } from './productionLogger';
import { 
  ENV_CONFIG, 
  PERFORMANCE_CONFIG, 
  SECURITY_HEADERS, 
  SIGNING_CONFIG,
  DEBUG_CONFIG 
} from '../config/environment';

// Safely import NetInfo with fallback
let NetInfo: any = null;
try {
  NetInfo = require('@react-native-community/netinfo').default;
} catch (error) {
  logger.warn('NetInfo module not available, network monitoring disabled', error, 'network');
}

interface RequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTime?: number;
  skipAuth?: boolean;
}

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  signal?: AbortSignal;
}

interface ApiError extends Error {
  status?: number;
  response?: any;
  code?: string;
  userDeleted?: boolean;
  redirectToSignup?: boolean;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
}

class ApiService {
  private baseURL: string;
  private cache = new Map<string, CacheEntry>();
  private requestQueue: Array<() => Promise<any>> = [];
  private isOnline = true;
  private maxCacheSize = 100;
  private defaultTimeout = 15000; // 15 seconds (optimized for faster response)
  private defaultRetries = 2; // Reduced retries for faster failure detection
  private defaultRetryDelay = 500; // Reduced delay for faster retries
  private pendingRequests = new Map<string, Promise<any>>(); // Request deduplication
  private secureFetch: typeof fetch | null = null;

  constructor() {
    // Use environment-based URLs
    this.baseURL = this.getBaseURL();
    
    // Initialize network monitoring
    this.initializeNetworkMonitoring();
    
    // Clean cache periodically
    this.startCacheCleanup();
    
    // Initialize secure fetch with SSL pinning
    this.initializeSecureFetch();
  }

  private getBaseURL() {
    return ENV_CONFIG.API_BASE_URL;
  }

  private async initializeSecureFetch() {
    try {
      // Initialize SSL pinning service and get secure fetch
      this.secureFetch = await sslPinning.createSecureFetch();
      logger.info('Secure fetch with SSL pinning initialized', null, 'api');
    } catch (error) {
      logger.error('Failed to initialize secure fetch, falling back to regular fetch', error, 'api');
      this.secureFetch = fetch; // Fallback to regular fetch
      
      // Record non-fatal error
      crashReporting.recordNonFatalError('SSL pinning initialization failed', {
        error: error instanceof Error ? error.message : String(error),
        fallbackUsed: true,
      });
    }
  }

  private async initializeNetworkMonitoring() {
    try {
      // Check if NetInfo is available and properly initialized
      if (!NetInfo) {
        logger.info('NetInfo not available, using default online state', null, 'network');
        this.isOnline = true;
        return;
      }

      // Test if NetInfo methods are available
      if (typeof NetInfo.fetch !== 'function' || typeof NetInfo.addEventListener !== 'function') {
        logger.warn('NetInfo methods not available, using default online state', null, 'network');
        this.isOnline = true;
        return;
      }

      // Get initial network state with timeout
      const initialStatePromise = NetInfo.fetch();
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('NetInfo fetch timeout')), 5000)
      );

      const initialState = await Promise.race([initialStatePromise, timeoutPromise]);
      this.isOnline = initialState.isConnected ?? true;
      
      logger.info('Initial network state determined', {
        isConnected: initialState.isConnected,
        type: initialState.type,
        isInternetReachable: initialState.isInternetReachable
      }, 'network');

      // Monitor network connectivity
      const unsubscribe = NetInfo.addEventListener((state: any) => {
        const wasOnline = this.isOnline;
        this.isOnline = state.isConnected ?? true;
        
        logger.info('Network state changed', {
          isConnected: state.isConnected,
          type: state.type,
          isInternetReachable: state.isInternetReachable,
          wasOnline,
          isOnline: this.isOnline
        }, 'network');
        
        if (!wasOnline && this.isOnline) {
          // Back online - process queued requests
          this.processRequestQueue();
        }
        
        try {
          crashReporting.addBreadcrumb({
            category: 'network',
            message: `Network ${this.isOnline ? 'connected' : 'disconnected'}`,
            level: this.isOnline ? 'info' : 'warning',
            data: { connectionType: state.type, isConnected: state.isConnected },
          });
        } catch (breadcrumbError) {
          logger.warn('Failed to add network breadcrumb', breadcrumbError, 'network');
        }
      });
      
      return unsubscribe;
    } catch (error) {
      logger.warn('Network monitoring initialization failed', error, 'network');
      // Fallback to assuming online for production stability
      this.isOnline = true;
    }
  }

  private startCacheCleanup() {
    // Clean expired cache entries every 5 minutes
    setInterval(() => {
      this.cleanExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * Get stored access token - SECURE VERSION
   */
  async getAccessToken(): Promise<string | null> {
    try {
      const { accessToken } = await secureStorage.getAuthTokens();
      return accessToken;
    } catch (error) {
      logger.error('Error getting access token', error, 'auth');
      return null;
    }
  }

  /**
   * Get stored refresh token - SECURE VERSION
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      const { refreshToken } = await secureStorage.getAuthTokens();
      return refreshToken;
    } catch (error) {
      logger.error('Error getting refresh token', error, 'auth');
      return null;
    }
  }

  /**
   * Store tokens - SECURE VERSION
   */
  async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      if (accessToken && refreshToken) {
        const success = await secureStorage.storeAuthTokens(accessToken, refreshToken);
        if (!success) {
          throw new Error('Failed to store tokens securely');
        }
        logger.info('Tokens stored securely', null, 'auth');
      }
    } catch (error) {
      logger.error('Error storing tokens', error, 'auth');
      throw error; // Re-throw to let caller handle
    }
  }

  /**
   * Clear stored tokens - SECURE VERSION
   */
  async clearTokens(): Promise<void> {
    try {
      const success = await secureStorage.clearAuthData();
      if (success) {
        logger.info('Tokens cleared securely', null, 'auth');
      } else {
        logger.warn('Failed to clear some tokens', null, 'auth');
      }
    } catch (error) {
      logger.error('Error clearing tokens', error, 'auth');
    }
  }

  // Cache management
  private getCacheKey(endpoint: string, options: any): string {
    const method = options.method || 'GET';
    const body = options.body || '';
    return `${method}:${endpoint}:${body}`;
  }

  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setCache(key: string, data: any, cacheTime: number = 5 * 60 * 1000) {
    // Limit cache size
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + cacheTime,
    });
  }

  private cleanExpiredCache() {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    for (const [key, entry] of entries) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Request queue for offline support
  private async processRequestQueue() {
    if (!this.isOnline || this.requestQueue.length === 0) return;
    
    logger.info(`Processing ${this.requestQueue.length} queued requests`, null, 'queue');
    
    const queue = [...this.requestQueue];
    this.requestQueue = [];
    
    for (const request of queue) {
      try {
        await request();
      } catch (error) {
        logger.error('Failed to process queued request', error, 'queue');
      }
    }
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Enhanced request method with retry, caching, and error handling
  async request<T = any>(endpoint: string, options: RequestOptions = {}, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = this.defaultRetryDelay,
      cache = false,
      cacheTime = 5 * 60 * 1000, // 5 minutes
      skipAuth = false,
    } = config;

    const method = options.method || 'GET';
    const url = `${this.baseURL}${endpoint}`;
    const cacheKey = this.getCacheKey(endpoint, options);

    // PERFORMANCE OPTIMIZATION: Request deduplication for identical requests
    const requestKey = `${method}:${endpoint}:${options.body || ''}`;
    if (this.pendingRequests.has(requestKey)) {
      logger.debug(`Deduplicating identical request: ${method} ${endpoint}`, null, 'dedup');
      return this.pendingRequests.get(requestKey)!;
    }

    // Check cache for GET requests
    if (method === 'GET' && cache) {
      const cachedData = this.getFromCache(cacheKey);
      if (cachedData) {
        logger.debug(`Cache hit for ${method} ${endpoint}`, null, 'cache');
        return cachedData;
      }
    }

    // Check if offline and queue request
    if (!this.isOnline && method !== 'GET') {
      return new Promise((resolve, reject) => {
        this.requestQueue.push(async () => {
          try {
            const result = await this.request(endpoint, options, config);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      });
    }

    // Start performance tracking
    const perfId = performanceService.startTiming(`api_${method}_${endpoint.replace(/\//g, '_')}`);
    const startTime = Date.now();

    let lastError: ApiError = new Error('Request failed') as ApiError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // Default headers
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'X-Request-ID': `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          'X-Platform': Platform.OS,
          'X-App-Version': '1.0.0', // TODO: Get from app config
          ...options.headers,
        };

        // Add authorization header if token exists and not skipped
        if (!skipAuth) {
          const accessToken = await this.getAccessToken();
          logger.debug(`🔑 [API] Token retrieval for ${method} ${endpoint}`, { 
            hasToken: !!accessToken, 
            tokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'No token',
            skipAuth 
          });
          
          if (accessToken) {
            headers.Authorization = `Bearer ${accessToken}`;
          } else {
            logger.warn(`⚠️ [API] No access token available for ${method} ${endpoint}`);
          }
        }

        logger.api(method, endpoint, 0, 0, { attempt: attempt + 1, maxAttempts: retries + 1 });

        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        // Use secure fetch with SSL pinning if available, otherwise fallback to regular fetch
        const fetchFunction = this.secureFetch || fetch;
        const response = await fetchFunction(url, {
          ...options,
          headers,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        let data;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          data = await response.text();
        }

        const duration = Date.now() - startTime;

        // Track API performance
        performanceService.trackApiCall(method, endpoint, duration, response.status);
        performanceService.endTiming(perfId);

        // Add breadcrumb for API call
        crashReporting.recordApiCall(method, endpoint, response.status, duration);

        if (!response.ok) {
          // Handle user deletion case FIRST (before token expiration)
          if (response.status === 404 && data?.code === 'USER_DELETED') {
            logger.security('USER_DELETED_RESPONSE_RECEIVED', { 
              endpoint, 
              userId: data.data?.userId 
            });
            
            // Clear tokens immediately
            await this.clearTokens();
            
            // Create specific error for user deletion
            const userDeletedError = new Error(data.message || 'User account no longer exists') as ApiError;
            userDeletedError.status = 404;
            userDeletedError.code = 'USER_DELETED';
            userDeletedError.userDeleted = true;
            userDeletedError.redirectToSignup = data.data?.redirectToSignup || true;
            throw userDeletedError;
          }

          // Handle token expiration
          if (response.status === 401 && !skipAuth && data.message?.includes('token')) {
            logger.info('Token expired, attempting refresh', null, 'auth');
            const refreshed = await this.refreshToken();
            if (refreshed) {
              // Retry the original request with new token
              return this.request(endpoint, options, config);
            } else {
              // Refresh failed, clear tokens and throw error
              await this.clearTokens();
              const sessionError: ApiError = new Error('Session expired. Please login again.') as ApiError;
              sessionError.status = 401;
              throw sessionError;
            }
          }

          // Handle rate limiting
          if (response.status === 429) {
            const retryAfter = response.headers.get('Retry-After');
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : retryDelay * Math.pow(2, attempt);
            
            if (attempt < retries) {
              logger.warn(`Rate limited, retrying after ${delay}ms`, { delay, attempt }, 'api');
              await this.sleep(delay);
              continue;
            }
          }

          // Handle server errors (5xx) with retry
          if (response.status >= 500 && attempt < retries) {
            const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
            logger.warn(`Server error ${response.status}, retrying after ${delay}ms`, { status: response.status, delay, attempt }, 'api');
            await this.sleep(delay);
            continue;
          }

          const error: ApiError = new Error(data.message || `HTTP ${response.status}`);
          error.status = response.status;
          error.response = data;
          throw error;
        }

        const result = { data, status: response.status };

        // Cache successful GET requests
        if (method === 'GET' && cache && response.status === 200) {
          this.setCache(cacheKey, result, cacheTime);
        }

        logger.api(method, endpoint, response.status, duration);
        return result;

      } catch (error) {
        // Convert unknown error to ApiError
        const apiError: ApiError = error instanceof Error 
          ? error as ApiError
          : new Error(String(error)) as ApiError;
        
        lastError = apiError;
        
        // Don't retry on certain errors
        if (
          apiError.name === 'AbortError' || // Timeout
          apiError.status === 400 || // Bad request
          apiError.status === 401 || // Unauthorized
          apiError.status === 403 || // Forbidden
          apiError.status === 404 || // Not found
          apiError.message.includes('JSON')  // JSON parse error
        ) {
          logger.api(method, endpoint, apiError.status || 500, Date.now() - startTime, { error: apiError.message });
          break;
        }
        
        // Retry for other errors if attempts remain
        if (attempt < retries) {
          const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
          logger.warn(`${method} ${endpoint} - Error, retrying in ${delay}ms`, { error: apiError.message, delay, attempt }, 'api');
          await this.sleep(delay);
          continue;
        }
        
        logger.error(`${method} ${endpoint} - Error after ${attempt + 1} attempts`, apiError, 'api');
      }
    }
    
    // If we got here, all attempts failed
    throw lastError;
  }

  // Refresh access token
  async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = await this.getRefreshToken();
      if (!refreshToken) {
        logger.warn('No refresh token available for refresh', null, 'auth');
        return false;
      }
      
      logger.info('Attempting to refresh token', null, 'auth');
      
      // Use secure fetch with SSL pinning if available, otherwise fallback to regular fetch
      const fetchFunction = this.secureFetch || fetch;
      const response = await fetchFunction(`${this.baseURL}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });
      
      if (!response.ok) {
        logger.warn(`Token refresh failed with status ${response.status}`, { status: response.status }, 'auth');
        return false;
      }
      
      const data = await response.json();
      
      if (data.status === 'success' && data.data?.tokens) {
        const { accessToken, refreshToken: newRefreshToken } = data.data.tokens;
        await this.storeTokens(accessToken, newRefreshToken);
        logger.info('Token refreshed successfully', null, 'auth');
        return true;
      }
      
      logger.warn('Invalid response from refresh token endpoint', null, 'auth');
      return false;
    } catch (error) {
      logger.error('Error refreshing token', error, 'auth');
      return false;
    }
  }

  // HTTP methods
  async get(endpoint: string, params: Record<string, any> = {}, config: RequestConfig = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    // Disable caching for critical authentication endpoints
    const criticalEndpoints = ['/setup/status', '/user/profile', '/auth/verify', '/email/verify-otp'];
    const isCriticalEndpoint = criticalEndpoints.some(critical => endpoint.includes(critical));
    
    // Enable caching by default for GET requests, but disable for critical auth endpoints
    const getConfig = { 
      cache: isCriticalEndpoint ? false : true, 
      ...config 
    };
    
    if (isCriticalEndpoint) {
      logger.info(`🚫 No caching for critical endpoint: ${endpoint}`, null, 'cache');
    }
    
    return this.request(url, { method: 'GET' }, getConfig);
  }

  async post(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    }, config);
  }

  async put(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, config);
  }

  async patch(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    }, config);
  }

  async delete(endpoint: string, config: RequestConfig = {}) {
    return this.request(endpoint, { method: 'DELETE' }, config);
  }

  async uploadFile(endpoint: string, file: any, fieldName: string = 'file', additionalData: Record<string, any> = {}, config: RequestConfig = {}) {
    try {
      logger.info(`Uploading file to ${endpoint}`, null, 'upload');
      
      // Create form data
      const formData = new FormData();
      formData.append(fieldName, file);
      
      // Add additional data
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
      
      // Get access token if needed
      let headers: Record<string, string> = {};
      if (!config.skipAuth) {
        const accessToken = await this.getAccessToken();
        if (accessToken) {
          headers.Authorization = `Bearer ${accessToken}`;
        }
      }
      
      // Add platform and app version
      headers['X-Platform'] = Platform.OS;
      headers['X-App-Version'] = '1.0.0'; // TODO: Get from app config
      
      // Start performance tracking
      const perfId = performanceService.startTiming(`api_upload_${endpoint.replace(/\//g, '_')}`);
      
      // Make request with secure fetch
      const fetchFunction = this.secureFetch || fetch;
      const response = await fetchFunction(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });
      
      // End performance tracking
      performanceService.endTiming(perfId);
      
      // Parse response
      let data;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
      
      if (!response.ok) {
        const error: ApiError = new Error(data.message || `HTTP ${response.status}`);
        error.status = response.status;
        error.response = data;
        throw error;
      }
      
      logger.info(`Upload ${endpoint} - Success`, null, 'upload');
      return { data, status: response.status };
      
    } catch (error) {
      // Convert unknown error to ApiError
      const apiError: ApiError = error instanceof Error 
        ? error as ApiError
        : new Error(String(error)) as ApiError;
      
      logger.error(`❌ [API] Upload ${endpoint} - Error:`, { error: apiError.message }, 'upload');
      throw apiError;
    }
  }

  // Authentication endpoints
  async sendOTP(phoneNumber: string): Promise<any> {
    logger.info(`📱 [API] Sending OTP to ${phoneNumber}`, null, 'otp');
    return this.request('auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ phoneNumber }),
    });
  }

  async verifyOTP(phoneNumber: string, otp: string): Promise<any> {
    logger.info(`🔐 [API] Verifying OTP for ${phoneNumber}`, null, 'otp');
    const response = await this.request('auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify({ phoneNumber, otp }),
    });

    // Store tokens after successful verification
    if (response.data && response.data.tokens) {
      const { accessToken, refreshToken } = response.data.tokens;
      await this.storeTokens(accessToken, refreshToken);
    }

    return response;
  }

  // Email endpoints
  async sendEmailOTP(email: string, purpose: string = 'verification'): Promise<any> {
    logger.info(`📧 [API] Sending email OTP to ${email} for ${purpose}`, null, 'email');
    return this.request('/email/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email, purpose }),
    }, { 
      cache: false, // No caching for OTP requests
      skipAuth: true, // OTP sending doesn't require auth
      timeout: 12000, // Shorter timeout for OTP sending (12 seconds)
      retries: 1 // Only 1 retry for OTP sending to fail fast
    });
  }

  async verifyEmailOTP(email: string, otp: string): Promise<any> {
    logger.info(`🔐 [API] Verifying email OTP for ${email}`, null, 'email');
    const response = await this.request('/email/verify-otp', {
      method: 'POST',
      body: JSON.stringify({ email, otp }),
    }, { 
      cache: false, // No caching for verification
      skipAuth: true, // OTP verification doesn't require auth initially
      timeout: 10000, // Shorter timeout for verification (10 seconds)
      retries: 1 // Only 1 retry for verification to fail fast
    });

    // Store tokens after successful verification
    if (response.data && response.data.tokens) {
      const { accessToken, refreshToken } = response.data.tokens;
      await this.storeTokens(accessToken, refreshToken);
    }

    return response;
  }

  // SMS endpoints
  async resendOTP(): Promise<any> {
    logger.info(`🔄 [API] Resending OTP`, null, 'otp');
    return this.request('sms/resend-otp', {
      method: 'POST',
    });
  }
}

export const apiService = new ApiService();
export default apiService;

export const verifyEmailOtp = async (data: { email: string; otp: string }) => {
  try {
    const response = await apiService.post('/email/verify-otp', data);
    
    if (response.data?.status === 'success') {
      // Store tokens if they exist in the response
      if (response.data.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await apiService.storeTokens(accessToken, refreshToken);
      }
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    }
    
    throw new Error(response.data?.message || 'Verification failed');
  } catch (error) {
    logger.error('❌ [API] Email verification error:', { error }, 'email');
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && error.response.data && typeof error.response.data === 'object' && 'message' in error.response.data && typeof error.response.data.message === 'string') {
      throw new Error(error.response.data.message || 'Verification failed');
    }
    throw new Error('Verification failed');
  }
};
