"use client"

import React, { useState, useRef, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, TextInput, Animated, Alert } from "react-native"
import { useTheme } from "../components/ThemeContext"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../types/navigation"
import ApiService from "../services/apiService"
import ArrowRightIcon from "../components/icons/ArrowRightIcon"
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, "PhoneInput">

const PhoneInputScreen = ({ }: Props) => {
  const { theme, isDark } = useTheme()
  const [phoneNumber, setPhoneNumber] = useState("")
  const [isPhoneFocused, setIsPhoneFocused] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false)

  // Animations
  const fadeAnim = useRef(new Animated.Value(0)).current
  const slideUpAnim = useRef(new Animated.Value(30)).current
  const labelAnim = useRef(new Animated.Value(0)).current

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()
  }, [])

  // Enhanced input sanitization
  const sanitizePhoneInput = useCallback((input: string): string => {
    // Remove all non-digits and limit length for security
    return input.replace(/[^\d]/g, '').slice(0, 15);
  }, []);

  const validateNigerianPhone = useCallback((phone: string): boolean => {
    const cleaned = sanitizePhoneInput(phone);
    return /^0[789]\d{9}$/.test(cleaned) && cleaned.length === 11;
  }, [sanitizePhoneInput]);

  const formatNigerianPhoneNumber = useCallback((text: string) => {
    // Sanitize input first
    let cleaned = sanitizePhoneInput(text)

    // Handle different Nigerian number formats
    if (cleaned.startsWith("234")) {
      // International format: +234XXXXXXXXXX - convert to local format with leading 0
      cleaned = "0" + cleaned.slice(3) // Remove country code and add leading 0
    } else if (!cleaned.startsWith("0") && cleaned.length > 0) {
      // If number doesn't start with 0, add it
      cleaned = "0" + cleaned
    }

    // Limit to 11 digits (Nigerian mobile numbers with leading 0)
    cleaned = cleaned.slice(0, 11)

    // Format as 0XXX XXX XXXX
    if (cleaned.length >= 8) {
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 11)}`
    } else if (cleaned.length >= 4) {
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`
    } else {
      return cleaned
    }
  }, [])

  // Memoized handlers for performance with enhanced security
  const handleContinue = useCallback(async () => {
    // Enhanced validation with sanitization
    const cleanedNumber = sanitizePhoneInput(phoneNumber);
    const isValidPhone = validateNigerianPhone(cleanedNumber);
    
    if (!isValidPhone) {
      logger.security('INVALID_PHONE_INPUT_ATTEMPT', {
        inputLength: cleanedNumber.length,
        startsCorrectly: cleanedNumber.startsWith('0'),
        phonePrefix: cleanedNumber.slice(0, 4)
      });
      Alert.alert('Invalid Phone Number', 'Please enter a valid Nigerian phone number (11 digits starting with 07, 08, or 09)');
      return;
    }
    
    if (isValidPhone && !isLoading) {
      setIsLoading(true)

      try {
        logger.userAction('phone_otp_requested', {
          phonePrefix: cleanedNumber.slice(0, 4) + '***',
          phoneLength: cleanedNumber.length
        });
        
        const response = await ApiService.sendOTP(cleanedNumber)

        if (response.status === 'success') {
          logger.info('OTP sent successfully', {
            phonePrefix: cleanedNumber.slice(0, 4) + '***'
          }, 'phone_input');
          navigationHandler.navigateToPhoneVerification(cleanedNumber)
        } else {
          logger.warn('Failed to send OTP', {
            error: response.message,
            phonePrefix: cleanedNumber.slice(0, 4) + '***'
          }, 'phone_input');
          Alert.alert('Error', response.message || 'Failed to send verification code')
        }
      } catch (error) {
        logger.error('Error sending OTP', error, 'phone_input');
        const errorMessage = (typeof error === 'object' && error !== null && 'message' in error)
          ? (error as { message?: string }).message
          : undefined;
        Alert.alert('Error', errorMessage || 'Failed to send verification code. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }
  }, [phoneNumber, isLoading])

  const handleBack = useCallback(() => {
    navigationHandler.goBack()
  }, [])

  const handlePhoneChange = useCallback((text: string) => {
    const formatted = formatNigerianPhoneNumber(text)
    setPhoneNumber(formatted)
  }, [formatNigerianPhoneNumber])

  // Memoized focus handlers
  const handleFocus = useCallback(() => {
    setIsPhoneFocused(true)
    Animated.timing(labelAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start()
  }, [labelAnim])

  const handleBlur = useCallback(() => {
    setIsPhoneFocused(false)
    if (!phoneNumber) {
      Animated.timing(labelAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start()
    }
  }, [phoneNumber, labelAnim])

  // Enhanced validation with sanitization
  const cleanedNumber = sanitizePhoneInput(phoneNumber)
  const isValidPhone = validateNigerianPhone(cleanedNumber)

  // Memoized styles for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.1)',
    },
    backButtonPressed: {
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)',
      transform: [{ scale: 0.95 }],
    },
    content: {
      flex: 1,
      paddingHorizontal: 30,
      paddingTop: 40,
    },
    inputContainer: {
      marginBottom: 80,
      position: 'relative',
    },
    floatingLabel: {
      position: 'absolute',
      left: 0,
      color: theme.colors.text,
      opacity: 0.7,
    },
    input: {
      borderWidth: 0,
      borderBottomWidth: 2,
      borderBottomColor: isPhoneFocused ? (isDark ? '#FFFFFF' : '#000000') : 'rgba(128, 128, 128, 0.3)',
      paddingVertical: 16,
      fontSize: 18,
      color: theme.colors.text,
      backgroundColor: 'transparent',
    },
    continueButton: {
      backgroundColor: '#000000', // Black button for both themes
      borderRadius: 25,
      paddingVertical: 18,
      paddingHorizontal: 60,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12,
      // Significantly enhanced shadow for better button appearance
      shadowColor: isDark ? '#FFFFFF' : '#000000', // White shadow in dark mode, black in light mode
      shadowOffset: {
        width: 0,
        height: 6, // Larger offset for more pronounced shadow
      },
      shadowOpacity: isDark ? 0.2 : 0.4, // Adjusted opacity based on theme
      shadowRadius: 8, // Larger radius for more diffuse shadow
      elevation: 10, // Higher elevation for Android
      borderWidth: 0, // No border
      opacity: (isValidPhone && !isLoading) ? 1 : 0.5,
    },
    continueButtonText: {
      color: isDark ? '#FFFFFF' : '#FFFFFF', // White text for both themes for good contrast
      fontSize: 16, // Bigger font size
      fontWeight: '700', // Bolder text
      letterSpacing: 0.5, // More letter spacing
    },
    footer: {
      paddingHorizontal: 30,
      paddingBottom: 40,
    },
    footerText: {
      fontSize: 13,
      color: theme.colors.text,
      opacity: 0.6,
      textAlign: "center",
      lineHeight: 20,
    },
  }), [theme, isDark, isPhoneFocused, isValidPhone, isLoading])

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor={theme.colors.background}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={[
            styles.backButton,
            isBackButtonPressed && styles.backButtonPressed
          ]}
          onPress={handleBack}
          onPressIn={() => setIsBackButtonPressed(true)}
          onPressOut={() => setIsBackButtonPressed(false)}
          activeOpacity={0.8}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Animated.View 
            style={{ 
              transform: [
                { rotate: '180deg' },
                { translateX: isBackButtonPressed ? -1 : 0 }
              ]
            }}
          >
            <ArrowRightIcon 
              size={18} 
              color={theme.colors.text} 
            />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideUpAnim }],
          }
        ]}
      >
        <View style={styles.inputContainer}>
          <Animated.Text
            style={[
              styles.floatingLabel,
              {
                fontSize: labelAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [18, 14],
                }),
                top: labelAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [16, -20],
                }),
              }
            ]}
          >
            Enter your phone number
          </Animated.Text>
          <TextInput
            style={styles.input}
            value={phoneNumber}
            onChangeText={handlePhoneChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder=""
            keyboardType="phone-pad"
            maxLength={15} // Accommodate 0XXX XXX XXXX format
          />
        </View>
      </Animated.View>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
          disabled={!isValidPhone || isLoading}
          activeOpacity={0.8}
        >
          <Text style={styles.continueButtonText}>
            {isLoading ? 'Sending...' : 'Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default PhoneInputScreen