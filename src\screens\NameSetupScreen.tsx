import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Animated,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../types/navigation';
import { useTheme } from '../components/ThemeContext';
import ArrowRightIcon from '../components/icons/ArrowRightIcon';
import GlassyBox from '../components/GlassyBox';
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

type Props = NativeStackScreenProps<RootStackParamList, 'NameSetup'>;

const NameSetupScreen = ({ route }: Props) => {
  const { theme, isDark } = useTheme();
  const [firstName, setFirstName] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const labelAnim = useRef(new Animated.Value(0)).current;

  const { userData } = route.params || { userData: {} };

  const isValidName = firstName.trim().length >= 2;

  useEffect(() => {
    Animated.timing(labelAnim, {
      toValue: isFocused || firstName ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, firstName]);

  const handleContinue = async () => {
    if (!isValidName) {
      Alert.alert('Invalid Name', 'First name must be at least 2 characters long');
      return;
    }

    setIsLoading(true);

    try {
      const { setupService } = await import('../services/setupService');

      await setupService.setupProfile({
        firstName: firstName.trim(),
      });

      logger.info('Profile saved successfully', { context: 'name-setup' }, 'auth');

      const updatedUserData = {
        ...userData,
        firstName: firstName.trim(),
        displayName: firstName.trim(),
      };  ``

      navigationHandler.navigateFromNameSetup(updatedUserData);
    } catch (error: any) {
      logger.error('Error saving profile', { context: 'name-setup', error }, 'auth');
      
      // Handle authentication errors specifically
      if (error.isAuthError || error.status === 401 || error.message?.includes('sign in again')) {
        Alert.alert(
          'Session Expired', 
          'Your session has expired. You will be redirected to sign in again.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to sign in screen
                navigationHandler.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', error.message || 'Failed to save profile. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const labelStyle = {
    position: 'absolute' as const,
    left: 0,
    color: isFocused 
      ? (isDark ? '#C084FC' : '#6B21A8') 
      : (isDark ? '#E5E7EB' : '#4B5563'),
    fontWeight: '500' as const,
    fontSize: labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    }),
    top: labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [12, -18],
    }),
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 40,
      justifyContent: 'flex-start',
    },
    welcomeContainer: {
      alignItems: 'center',
      marginBottom: 48,
    },
    title: {
      fontSize: 32,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      lineHeight: 24,
      textAlign: 'center',
      maxWidth: 280,
    },
    inputWrapper: {
      position: 'relative',
      marginTop: 32,
      paddingBottom: 4,
      borderBottomWidth: isFocused ? 0 : 2,
      borderColor: isDark ? '#4B5563' : '#9CA3AF',
    },
    inputGradientLine: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 2,
    },
    input: {
      height: 40,
      fontSize: 16,
      color: theme.colors.text,
      paddingVertical: 4,
    },
    continueButton: {
      borderRadius: 16,
      opacity: isValidName ? 1 : 0.6,
      overflow: 'hidden',
    },
    glassyButtonContainer: {
      borderRadius: 16,
      overflow: 'hidden',
    },
    buttonContent: {
      paddingVertical: 18,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.2)',
    },
    continueText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent={true}
      />
      <ImageBackground
        source={
          isDark
            ? require('../../assets/images/bg-name.jpeg')
            : require('../../assets/images/bg-light.jpeg')
        }
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      <View style={styles.header} />

      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <View style={styles.content}>
          <View style={styles.welcomeContainer}>
            <Text style={styles.title}>What's your name?</Text>
            <Text style={styles.subtitle}>Let's personalize your experience</Text>
          </View>

          <View style={styles.inputWrapper}>
            <Animated.Text style={labelStyle}>First Name</Animated.Text>
            <TextInput
              style={styles.input}
              value={firstName}
              onChangeText={setFirstName}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder=""
              autoCapitalize="words"
              autoCorrect={false}
            />
            {isFocused && (
              <LinearGradient
                colors={isDark 
                  ? ['#C084FC', '#FFFFFF']
                  : ['#6B21A8', '#000000']
                }
                style={styles.inputGradientLine}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            )}
          </View>
        </View>

        <View style={{ paddingHorizontal: 24, paddingBottom: 20 }}>
          <GlassyBox 
            intensity="medium"
            glow={isValidName}
            style={styles.glassyButtonContainer}
          >
            <TouchableOpacity
              style={styles.continueButton}
              onPress={handleContinue}
              disabled={!isValidName || isLoading}
              activeOpacity={0.8}
            >
              <View style={styles.buttonContent}>
                {isLoading ? (
                  <ActivityIndicator size="small" color={theme.colors.text} />
                ) : (
                  <>
                    <Text style={styles.continueText}>Continue</Text>
                    <ArrowRightIcon 
                      size={18} 
                      color={theme.colors.text}
                    />
                  </>
                )}
              </View>
            </TouchableOpacity>
          </GlassyBox>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default NameSetupScreen;
