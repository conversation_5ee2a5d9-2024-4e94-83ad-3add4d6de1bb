import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useTheme } from './ThemeContext';
import { logger } from '../services/productionLogger';
import crashReporting from '../services/crashReportingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundaryClass extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log to crash reporting service
    this.logErrorToService(error, errorInfo);
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);
  }

  logErrorToService = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Log to our logger first
      logger.error('🔥 [ErrorBoundary] Caught error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      }, 'ErrorBoundary');

      // Add breadcrumb for context
      crashReporting.addBreadcrumb({
        category: 'error_boundary',
        message: 'React Error Boundary caught error',
        level: 'error',
        data: {
          errorMessage: error.message,
          hasComponentStack: !!errorInfo.componentStack,
        },
      });

      // Record the error in Crashlytics
      await crashReporting.recordError(error, errorInfo.componentStack || undefined, true);

      logger.info('Error successfully reported to crash service', null, 'ErrorBoundary');
    } catch (logError) {
      logger.error('Failed to log error to crash service:', { logError }, 'ErrorBoundary');
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback error={this.state.error} onRetry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

// Error Fallback Component
const ErrorFallback: React.FC<{
  error: Error | null;
  onRetry: () => void;
}> = ({ error, onRetry }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    message: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 24,
    },
    errorDetails: {
      backgroundColor: theme.colors.card,
      borderRadius: 8,
      padding: 16,
      marginBottom: 24,
      maxHeight: 200,
      width: '100%',
    },
    errorText: {
      fontSize: 12,
      color: theme.colors.muted,
      fontFamily: 'monospace',
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 32,
      paddingVertical: 12,
      borderRadius: 8,
      marginBottom: 16,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    reportButton: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 32,
      paddingVertical: 12,
      borderRadius: 8,
    },
    reportButtonText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Oops! Something went wrong</Text>
      <Text style={styles.message}>
        We're sorry for the inconvenience. The app encountered an unexpected error.
      </Text>
      
      {__DEV__ && error && (
        <ScrollView style={styles.errorDetails}>
          <Text style={styles.errorText}>
            {error.message}
            {'\n\n'}
            {error.stack}
          </Text>
        </ScrollView>
      )}

      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.reportButton} onPress={() => {
        // TODO: Open feedback form or email
      logger.info('Report error tapped');
      }}>
        <Text style={styles.reportButtonText}>Report Issue</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

// HOC wrapper for functional components
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  return (props: P) => (
    <ErrorBoundaryClass fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundaryClass>
  );
};

export default ErrorBoundaryClass;