import { NavigationProp } from '@react-navigation/native';
import { Alert } from 'react-native';
import secureStorage from '../services/secureStorageService';
import { ENV_CONFIG } from '../config/environment';
import { logger } from '../services/productionLogger';
import { navigationHandler } from '../handlers/navigationHandler';

interface PinAttempt {
  count: number;
  lastAttempt: number;
  lockoutUntil?: number;
}

class AuthSecurity {
  private pinAttempts = new Map<string, PinAttempt>();
  
  /**
   * Check if PIN attempts are locked out
   */
  isPinLockedOut(identifier: string = 'global'): boolean {
    const attempt = this.pinAttempts.get(identifier);
    if (!attempt || !attempt.lockoutUntil) return false;
    
    const now = Date.now();
    if (now > attempt.lockoutUntil) {
      // Lockout expired, reset
      this.pinAttempts.delete(identifier);
      return false;
    }
    
    return true;
  }
  
  /**
   * Record a failed PIN attempt
   */
  recordFailedPinAttempt(identifier: string = 'global'): boolean {
    const now = Date.now();
    const current = this.pinAttempts.get(identifier) || { count: 0, lastAttempt: 0 };
    
    // Reset count if last attempt was more than 15 minutes ago
    if (now - current.lastAttempt > 15 * 60 * 1000) {
      current.count = 0;
    }
    
    current.count++;
    current.lastAttempt = now;
    
    // Lock out after max attempts
    if (current.count >= ENV_CONFIG.MAX_PIN_ATTEMPTS) {
      current.lockoutUntil = now + ENV_CONFIG.PIN_LOCKOUT_DURATION;
      logger.security('PIN_LOCKOUT_TRIGGERED', { 
        identifier,
        lockoutUntil: current.lockoutUntil,
        attempts: current.count 
      });
    }
    
    this.pinAttempts.set(identifier, current);
    return current.lockoutUntil ? true : false;
  }
  
  /**
   * Clear PIN attempts on successful verification
   */
  clearPinAttempts(identifier: string = 'global'): void {
    this.pinAttempts.delete(identifier);
  }
  
  /**
   * Get remaining lockout time
   */
  getLockoutTimeRemaining(identifier: string = 'global'): number {
    const attempt = this.pinAttempts.get(identifier);
    if (!attempt || !attempt.lockoutUntil) return 0;
    
    const remaining = attempt.lockoutUntil - Date.now();
    return Math.max(0, remaining);
  }
}

const authSecurity = new AuthSecurity();

/**
 * Handle authentication errors globally
 */
export const handleAuthError = async (error: any, navigation: NavigationProp<any>) => {
  const errorMessage = error.message || '';
  
  // Check if it's a session expiry error
  if (errorMessage.includes('Session expired') || 
      errorMessage.includes('expired token') ||
      errorMessage.includes('Invalid token') ||
      errorMessage.includes('Unauthorized')) {
    
    logger.security('SESSION_EXPIRED', { error: errorMessage });
    
    // Clear stored tokens securely
    try {
      await secureStorage.clearAuthData();
      logger.info('Auth tokens cleared securely', null, 'auth');
    } catch (clearError) {
      logger.error('Failed to clear auth tokens', clearError, 'auth');
    }
    
    // Show alert and redirect to login
    Alert.alert(
      'Session Expired',
      'Your session has expired. Please sign in again to continue.',
      [
        {
          text: 'Sign In Again',
          onPress: () => {
            navigationHandler.resetToScreen('Startup');
          }
        }
      ],
      { cancelable: false }
    );
    
    return true; // Indicates error was handled
  }
  
  return false; // Error not handled, let caller handle it
};

/**
 * Check if current tokens are valid - SECURE VERSION
 */
export const checkTokenValidity = async (): Promise<boolean> => {
  try {
    const { accessToken, refreshToken } = await secureStorage.getAuthTokens();
    
    if (!accessToken || !refreshToken) {
      return false;
    }
    
    // Basic JWT expiry check (without verification)
    try {
      const payload = JSON.parse(atob(accessToken.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      if (payload.exp && payload.exp < currentTime) {
        logger.info('Access token expired', null, 'auth');
        return false;
      }
      
      return true;
    } catch (parseError) {
      logger.error('Error parsing token', parseError, 'auth');
      return false;
    }
  } catch (error) {
    logger.error('Error checking token validity', error, 'auth');
    return false;
  }
};

/**
 * Clear all authentication data - SECURE VERSION
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    await secureStorage.clearAuthData();
    logger.info('Cleared all auth data securely', null, 'auth');
  } catch (error) {
    logger.error('Error clearing auth data', error, 'auth');
  }
};

/**
 * Enhanced PIN security functions
 */
export const pinSecurity = {
  /**
   * Check if PIN attempts are locked out
   */
  isPinLockedOut: (identifier?: string) => authSecurity.isPinLockedOut(identifier),
  
  /**
   * Record a failed PIN attempt
   */
  recordFailedAttempt: (identifier?: string) => authSecurity.recordFailedPinAttempt(identifier),
  
  /**
   * Clear PIN attempts on success
   */
  clearAttempts: (identifier?: string) => authSecurity.clearPinAttempts(identifier),
  
  /**
   * Get remaining lockout time in milliseconds
   */
  getLockoutTime: (identifier?: string) => authSecurity.getLockoutTimeRemaining(identifier),
  
  /**
   * Format lockout time for display
   */
  formatLockoutTime: (ms: number): string => {
    const minutes = Math.ceil(ms / (60 * 1000));
    return minutes === 1 ? '1 minute' : `${minutes} minutes`;
  }
};

/**
 * Secure PIN verification with rate limiting
 */
export const verifyPinSecurely = async (inputPin: string, identifier?: string): Promise<{ success: boolean; error?: string; lockoutTime?: number }> => {
  try {
    // Check if locked out
    if (pinSecurity.isPinLockedOut(identifier)) {
      const lockoutTime = pinSecurity.getLockoutTime(identifier);
      return {
        success: false,
        error: `Too many failed attempts. Try again in ${pinSecurity.formatLockoutTime(lockoutTime)}.`,
        lockoutTime
      };
    }
    
    // Verify PIN
    const isValid = await secureStorage.verifyUserPin(inputPin);
    
    if (isValid) {
      // Clear attempts on success
      pinSecurity.clearAttempts(identifier);
      return { success: true };
    } else {
      // Record failed attempt
      const isLockedOut = pinSecurity.recordFailedAttempt(identifier);
      
      if (isLockedOut) {
        const lockoutTime = pinSecurity.getLockoutTime(identifier);
        return {
          success: false,
          error: `Too many failed attempts. Account locked for ${pinSecurity.formatLockoutTime(lockoutTime)}.`,
          lockoutTime
        };
      } else {
        const remainingAttempts = ENV_CONFIG.MAX_PIN_ATTEMPTS - (authSecurity['pinAttempts'].get(identifier || 'global')?.count || 0);
        return {
          success: false,
          error: `Incorrect PIN. ${remainingAttempts} attempts remaining.`
        };
      }
    }
  } catch (error) {
    logger.error('Error verifying PIN', error, 'auth');
    return {
      success: false,
      error: 'Unable to verify PIN. Please try again.'
    };
  }
};

/**
 * Store PIN securely
 */
export const storePinSecurely = async (pin: string): Promise<boolean> => {
  try {
    return await secureStorage.storeUserPin(pin);
  } catch (error) {
    logger.error('Error storing PIN', error, 'auth');
    return false;
  }
};

/**
 * Check if biometric is available and enabled
 */
export const isBiometricEnabled = async (): Promise<boolean> => {
  try {
    const isAvailable = await secureStorage.isBiometricAvailable();
    const isToggleEnabled = await secureStorage.getBiometricToggle();
    return isAvailable && isToggleEnabled;
  } catch (error) {
    logger.error('Error checking biometric status', error, 'auth');
    return false;
  }
};

/**
 * Enable or disable biometric authentication
 */
export const setBiometricEnabled = async (enabled: boolean): Promise<boolean> => {
  try {
    return await secureStorage.setBiometricToggle(enabled);
  } catch (error) {
    logger.error('Error setting biometric toggle', error, 'auth');
    return false;
  }
};

/**
 * Validate PIN format
 */
export const validatePinFormat = (pin: string): { isValid: boolean; error?: string } => {
  if (!pin) {
    return { isValid: false, error: 'PIN is required' };
  }
  
  if (pin.length !== 4) {
    return { isValid: false, error: 'PIN must be exactly 4 digits' };
  }
  
  if (!/^\d{4}$/.test(pin)) {
    return { isValid: false, error: 'PIN must contain only numbers' };
  }
  
  // Check for weak patterns
  if (/^(.)\1{3}$/.test(pin)) {
    return { isValid: false, error: 'PIN cannot be the same digit repeated' };
  }
  
  if (/^(0123|1234|2345|3456|4567|5678|6789|9876|8765|7654|6543|5432|4321|3210)$/.test(pin)) {
    return { isValid: false, error: 'PIN cannot be a sequential pattern' };
  }
  
  return { isValid: true };
};

export { authSecurity };
